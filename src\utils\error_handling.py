"""Error handling utilities for the application."""

from __future__ import annotations

import functools
import logging
import sys
from collections.abc import Callable
from enum import Enum, auto
from types import TracebackType
from typing import Any, TypeVar, cast

from src.utils.logger import LoggerFactory

# Initialize logger with module name
logger = LoggerFactory().get_logger(__name__)

# Type variable for generic function typing
F = TypeVar('F', bound=Callable[..., Any])

class ErrorType(Enum):
    """Enumeration of error types."""
    VALIDATION = auto()
    CONFIGURATION = auto()
    RUNTIME = auto()
    IO = auto()
    NETWORK = auto()
    PERMISSION = auto()
    RESOURCE = auto()

class ApplicationError(Exception):
    """Base exception for application-specific errors."""
    def __init__(self, message: str, error_type: ErrorType = ErrorType.RUNTIME) -> None:
        """Initialize application error.

        Args:
            message: Error message
            error_type: Type of error (default: RUNTIME)
        """
        self.error_type = error_type
        super().__init__(message)

class ValidationError(ApplicationError):
    """Raised when validation of input data fails."""
    def __init__(self, message: str) -> None:
        super().__init__(message, ErrorType.VALIDATION)

class ConfigurationError(ApplicationError):
    """Raised when there is a configuration error."""
    def __init__(self, message: str) -> None:
        super().__init__(message, ErrorType.CONFIGURATION)

class RuntimeError(ApplicationError):
    """Raised when a runtime error occurs."""
    def __init__(self, message: str) -> None:
        super().__init__(message, ErrorType.RUNTIME)

class IOError(ApplicationError):
    """Raised when an I/O operation fails."""
    def __init__(self, message: str) -> None:
        super().__init__(message, ErrorType.IO)

class NetworkError(ApplicationError):
    """Raised when a network operation fails."""
    def __init__(self, message: str) -> None:
        super().__init__(message, ErrorType.NETWORK)

class PermissionError(ApplicationError):
    """Raised when a permission error occurs."""
    def __init__(self, message: str) -> None:
        super().__init__(message, ErrorType.PERMISSION)

class ResourceError(ApplicationError):
    """Raised when a resource is not available."""
    def __init__(self, message: str) -> None:
        super().__init__(message, ErrorType.RESOURCE)

def handle_errors(
    func: F | None = None,
    error_message: str = "",
    raise_exception: bool = True,
    log_level: int = logging.ERROR,
    reraise: bool = True,
) -> Callable[[F], F] | F:
    """Decorator to handle errors in functions.

    Args:
        func: The function to decorate
        error_message: Custom error message to log
        raise_exception: Whether to raise the exception after logging
        log_level: Logging level to use
        reraise: Whether to re-raise the exception after logging

    Returns:
        The decorated function
    """
    def decorator(f: F) -> F:
        @functools.wraps(f)
        def wrapper(*args: Any, **kwargs: Any) -> Any:
            try:
                return f(*args, **kwargs)
            except ApplicationError:
                raise
            except Exception as e:
                error_msg = error_message or f"Error in {f.__name__}"
                logger.log(log_level, error_msg, exc_info=sys.exc_info(),
                          extra={
                              "function_name": f.__name__,
                              "exception_type": type(e).__name__,
                              "exception_message": str(e)
                          })
                # 始终抛出异常，确保程序停止
                raise ApplicationError(str(e)) from e
        return cast(F, wrapper)

    if func is not None:
        return decorator(func)
    return decorator

class ErrorContext:
    """Context manager for error handling with custom error messages."""
    def __init__(self, context_name: str = "", log_level: int = logging.ERROR) -> None:
        self.context_name = context_name
        self.log_level = log_level

    def __enter__(self) -> None:
        pass

    def __exit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> bool:
        if exc_val is None:
            return False

        if isinstance(exc_val, ApplicationError):
            return False

        error_msg = f"Error in context: {self.context_name}" if self.context_name else "Error in context"
        logger.log(
            self.log_level,
            error_msg,
            exc_info=(exc_type, exc_val, exc_tb),
            extra={
                "context": self.context_name,
                "exception_type": exc_type.__name__ if exc_type else None,
                "exception_message": str(exc_val) if exc_val else None
            }
        )
        return True

def setup_global_exception_handlers() -> None:
    """Set up global exception handlers."""
    def handle_exception(
        exc_type: type[BaseException],  # type: ignore[valid-type]
        exc_value: BaseException | None,
        exc_traceback: TracebackType | None,
    ) -> None:
        """Handle uncaught exceptions globally.

        Args:
            exc_type: Type of the exception
            exc_value: The exception instance
            exc_traceback: Traceback object
        """
        # Handle keyboard interrupt and system exit specially
        if exc_type in (KeyboardInterrupt, SystemExit):
            if exc_value is not None and exc_traceback is not None:
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
            else:
                # 缺少必要的异常信息是错误状态，应该抛出异常
                error_msg = f"异常处理器参数不完整: exc_type={exc_type}, exc_value={exc_value}, exc_traceback={exc_traceback}"
                logger.critical(error_msg)
                raise RuntimeError(error_msg)
            return

        # Prepare extra info for logging
        extra = {}
        if exc_type is not None:
            extra["exception_type"] = exc_type.__name__
        if exc_value is not None:
            extra["exception_message"] = str(exc_value)

        # Safe logging with proper type checking
        try:
            if exc_type is not None and exc_value is not None and exc_traceback is not None:
                # Log with exc_info
                logger.critical(
                    "Uncaught exception",
                    exc_info=(exc_type, exc_value, exc_traceback),
                    extra=extra,
                )
                return

            # 如果缺少组件，这是一个严重错误，应该抛出异常
            error_msg = f"异常处理器参数不完整: exc_type={exc_type}, exc_value={exc_value}, exc_traceback={exc_traceback}"
            logger.critical(error_msg, extra=extra)
            raise RuntimeError(error_msg)
        except Exception as e:  # pylint: disable=broad-except
            # 日志记录失败是严重错误，应该抛出异常
            error_msg = f"记录未捕获异常失败: {e}"
            logger.critical(error_msg, extra={"original_exception": str(exc_value) if exc_value else "Unknown"})
            raise RuntimeError(error_msg) from e

    sys.excepthook = handle_exception

# Set up global exception handlers when the module is imported
setup_global_exception_handlers()

__all__ = [
    'ApplicationError',
    'ConfigurationError',
    'ErrorContext',
    'ErrorType',
    'IOError',
    'NetworkError',
    'PermissionError',
    'ResourceError',
    'RuntimeError',
    'ValidationError',
    'handle_errors',
    'setup_global_exception_handlers'
]
