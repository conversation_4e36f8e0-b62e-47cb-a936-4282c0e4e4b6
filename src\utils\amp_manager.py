"""混合精度训练管理模块 - 提供统一的混合精度训练支持

本模块集中管理混合精度训练相关功能，包括：
1. 自动混合精度上下文管理
2. 梯度缩放器管理
3. 混合精度配置验证

使用方式：
1. 初始化 AMPManager 实例
2. 使用 autocast_context() 方法获取自动混合精度上下文
3. 使用 scale_loss() 方法缩放损失
4. 使用 step_optimizer() 方法执行优化器步骤
5. 使用 update_scaler() 方法更新缩放器

示例：
```python
# 初始化
amp_manager = AMPManager(config.training.mixed_precision)

# 前向传播
with amp_manager.autocast_context():
    outputs = model(inputs)
    loss = criterion(outputs, targets)

# 反向传播
scaled_loss = amp_manager.scale_loss(loss)
scaled_loss.backward()

# 优化器步骤
amp_manager.step_optimizer(optimizer)
amp_manager.update_scaler()
```
"""

from collections.abc import Iterator  # 导入 Iterator
from contextlib import contextmanager
from typing import Any

import torch
from torch.amp.autocast_mode import autocast
from torch.amp.grad_scaler import GradScaler

from src.utils.cuda import cuda_manager
from src.utils.logger import get_logger


# 将 DummyScaler 定义移到 AMPManager 外部
class DummyScaler:
    def __init__(self, init_scale: float):
        if not isinstance(init_scale, int | float):
            raise ValueError("init_scale must be a number")
        self._scale = torch.tensor(float(init_scale))
        self._init_scale = float(init_scale)

    def scale(self, loss): return loss
    def unscale_(self, optimizer): pass
    def step(self, optimizer):
        if optimizer is not None:
            optimizer.step()
    def update(self, found_inf=False): pass
    def is_enabled(self): return False
    def get_scale(self): return 1.0
    def get_growth_factor(self): return 2.0
    def get_backoff_factor(self): return 0.5
    def get_growth_interval(self): return 2000
    def state_dict(self): return {"_scale": self._scale, "_init_scale": self._init_scale}
    def load_state_dict(self, state_dict):
        if "_scale" in state_dict:
            self._scale = state_dict["_scale"]
        if "_init_scale" in state_dict:
            self._init_scale = state_dict["_init_scale"]


class AMPManager:
    """混合精度训练管理器 - 提供统一的混合精度训练接口"""

    def __init__(self, config: Any = None, name: str = "default"):
        """初始化混合精度训练管理器

        Args:
            config: 混合精度配置对象，包含以下属性：
                - enabled: 是否启用混合精度
                - init_scale: 初始缩放因子
                - growth_factor: 缩放增长因子
                - backoff_factor: 缩放回退因子
                - growth_interval: 缩放增长间隔
                - dtype: 数据类型，默认为float16
            name: 管理器名称，用于日志标识
        """
        self.logger = get_logger(f"{self.__class__.__name__}_{name}")
        self.name = name

        # 验证CUDA可用性
        if cuda_manager.is_cuda_available:
            self.device = 'cuda'
        else:
            self.device = 'cpu'
            self.logger.warning("CUDA不可用，混合精度训练将不可用")

        # 初始化属性，设置默认值，这些属性将在 _parse_config 中被覆盖
        self.enabled = False
        self.dtype = torch.float16
        self.init_scale = 65536.0
        self.growth_factor = 2.0
        self.backoff_factor = 0.5
        self.growth_interval = 2000

        # 解析配置
        self._parse_config(config)

        # 初始化梯度缩放器
        self._init_scaler()

        self.logger.info(
            f"混合精度训练管理器初始化完成 ({name}):\n"
            f"- 启用状态: {'启用' if self.enabled else '禁用'}\n"
            f"- 数据类型: {self.dtype}\n"
            f"- 初始缩放因子: {self.init_scale}\n"
            f"- 增长因子: {self.growth_factor}\n"
            f"- 回退因子: {self.backoff_factor}\n"
            f"- 增长间隔: {self.growth_interval}"
        )

    def _set_required_params(self, config: Any) -> None:
        """设置基本参数，不论是否启用混合精度"""
        # 处理 dtype
        dtype_str = self._get_config_value(config, 'dtype')
        if dtype_str not in ['float16', 'bfloat16']:
            raise ValueError("dtype 必须是 'float16' 或 'bfloat16'")
        self.dtype = torch.bfloat16 if dtype_str == 'bfloat16' else torch.float16

        # 必需的参数列表
        required_params = [
            'init_scale',
            'growth_factor',
            'backoff_factor',
            'growth_interval'
        ]

        # 处理其他必需参数
        for param_name in required_params:
            try:
                value = self._get_config_value(config, param_name)

                # 转换和验证参数
                if param_name == 'growth_interval':
                    value = int(value)
                    if value <= 0:
                        raise ValueError("growth_interval 必须是正数")
                else:
                    value = float(value)

                setattr(self, param_name, value)
            except (ValueError, TypeError) as e:
                raise ValueError(f"参数 {param_name} 验证失败: {e!s}") from e

    def _get_config_value(self, config: Any, key: str) -> Any:
        """从配置对象中获取值

        Args:
            config: 配置对象
            key: 配置键名

        Returns:
            Any: 配置值

        Raises:
            ValueError: 如果配置对象为None或缺少指定键
        """
        if config is None:
            error_msg = "配置对象不能为None"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # 记录配置对象信息
        self.logger.debug(f"获取配置值 '{key}':")
        self.logger.debug(f"- 配置对象类型: {type(config)}")
        if hasattr(config, '__dict__'):
            self.logger.debug(f"- 配置对象属性: {dir(config)}")
            self.logger.debug(f"- 配置对象内容: {config.__dict__}")

        # 尝试从字典获取
        if isinstance(config, dict):
            if key not in config:
                error_msg = f"配置字典中缺少键 '{key}'"
                self.logger.error(error_msg)
                raise ValueError(error_msg)
            return config[key]

        # 移除hasattr检查，直接尝试获取属性
        try:
            value = getattr(config, key)
            self.logger.debug(f"- 获取到的值: {value} (类型: {type(value)})")
            return value
        except AttributeError:
            # 如果属性不存在，抛出异常
            error_msg = f"配置对象中缺少属性 '{key}'"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

    def _parse_config(self, config: Any) -> None:
        """解析混合精度配置

        Args:
            config: 混合精度配置对象
        """
        if config is None:
            self.logger.error("缺少混合精度配置")
            raise ValueError("必须提供混合精度配置")

        try:
            # 记录配置对象信息
            self.logger.debug(f"混合精度配置对象类型: {type(config)}")
            if hasattr(config, '__dict__'):
                self.logger.debug(f"混合精度配置对象属性: {dir(config)}")
                self.logger.debug(f"混合精度配置对象内容: {config.__dict__}")

            # 获取并验证启用状态
            try:
                # 必须显式指定 enabled 配置项
                self.enabled = bool(self._get_config_value(config, 'enabled'))
                self.logger.debug(f"混合精度启用状态: {self.enabled}")
            except ValueError as e:
                self.logger.error(f"获取enabled配置失败: {e!s}")
                raise ValueError("缺少必要的配置项: 'enabled'") from e

            # 如果启用了混合精度，但CUDA不可用，则报错
            if self.enabled and not cuda_manager.is_cuda_available:
                error_msg = "混合精度训练需要 CUDA 支持"
                raise RuntimeError(error_msg)

            # 设置基本参数（不论是否启用）
            try:
                self._set_required_params(config)
            except ValueError as e:
                self.logger.error(f"设置基本参数失败: {e!s}")
                raise

            # 验证参数
            if self.enabled:  # 只在启用时验证参数
                try:
                    self._validate_params()
                except ValueError as e:
                    self.logger.error(f"验证参数失败: {e!s}")
                    raise

        except Exception as e:
            self.logger.error(f"解析混合精度配置失败: {e!s}")
            raise ValueError(f"解析混合精度配置失败: {e!s}") from e

    def _validate_params(self) -> None:
        """验证参数的有效性和类型,如果无效则抛出异常"""
        # 检查所有必需参数是否存在
        required_params = ['dtype', 'init_scale', 'growth_factor', 'backoff_factor', 'growth_interval']
        for param_name in required_params:
            if getattr(self, param_name, None) is None:
                raise ValueError(f"缺少必要的参数: {param_name}")

        # 验证 dtype
        if not isinstance(self.dtype, torch.dtype):
            raise TypeError(f"dtype 必须是 torch.dtype 类型, 当前类型: {type(self.dtype)}")

        # 验证浮点参数
        float_params = {
            'init_scale': (0.0, float('inf')),  # (min, max)
            'growth_factor': (1.0, float('inf')),
            'backoff_factor': (0.0, 1.0)
        }

        for param_name, (min_val, max_val) in float_params.items():
            try:
                value = getattr(self, param_name)
                if not isinstance(value, int | float):
                    raise TypeError(f"{param_name} 必须是数字类型")
                value = float(value)
                if not min_val <= value <= max_val:
                    raise ValueError(f"{param_name} 必须在 [{min_val}, {max_val}] 范围内")
            except ValueError as e:
                raise ValueError(f"参数 {param_name} 验证失败: {e!s}") from e

        # 验证整数参数
        try:
            if not isinstance(self.growth_interval, int):
                self.growth_interval = int(self.growth_interval)
            if self.growth_interval <= 0:
                raise ValueError("growth_interval 必须大于 0")
        except (TypeError, ValueError) as e:
            raise ValueError(f"growth_interval 验证失败: {e!s}") from e

    def _init_scaler(self) -> None:
        """初始化梯度缩放器

        Raises:
            RuntimeError: 如果启用混合精度但 CUDA 不可用，或初始化失败时抛出
        """
        # 验证参数
        required_params = ['init_scale', 'growth_factor', 'backoff_factor', 'growth_interval']
        for param_name in required_params:
            if getattr(self, param_name, None) is None:
                raise ValueError(f"缺少必要的参数: {param_name}")

        # 在这里我们确保所有参数都已经存在并且类型正确
        init_scale = float(self.init_scale)
        growth_factor = float(self.growth_factor)
        backoff_factor = float(self.backoff_factor)
        growth_interval = int(self.growth_interval)

        if not self.enabled:
            self.scaler = DummyScaler(init_scale=init_scale)
            return

        if not cuda_manager.is_cuda_available:
            raise RuntimeError("混合精度训练需要 CUDA 支持")

        try:
            self.scaler = GradScaler(
                init_scale=init_scale,
                growth_factor=growth_factor,
                backoff_factor=backoff_factor,
                growth_interval=growth_interval,
                enabled=True
            )
        except Exception as e:
            self.logger.error(f"初始化 CUDA 梯度缩放器失败: {e!s}")
            raise RuntimeError(f"初始化 CUDA 梯度缩放器失败: {e!s}") from e

    @contextmanager
    def autocast_context(self, stream: Any = None) -> Iterator[None]:
        """获取自动混合精度上下文，必须指定CUDA流

        Args:
            stream: CUDA流，如果为None则使用默认流

        Returns:
            Iterator[None]: 上下文管理器生成器，用于混合精度计算环境

        Note:
            如果混合精度被禁用或CUDA不可用，将使用torch.float32
        """
        dtype = self.dtype if self.enabled and self.device == 'cuda' else torch.float32

        # 如果CUDA不可用，抛出异常
        if not cuda_manager.is_cuda_available:
            error_msg = "CUDA不可用，无法创建混合精度上下文"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg)

        # 如果没有指定流，使用默认流
        if stream is None:
            stream = cuda_manager.get_current_stream()
            if stream is None:
                stream = cuda_manager.create_stream("default_amp_stream")

        # 在指定流上下文中执行
        with torch.cuda.stream(stream), autocast(self.device, dtype=dtype, enabled=bool(self.enabled)):
            yield None

    def scale_loss(self, loss: torch.Tensor) -> torch.Tensor:
        """缩放损失

        Args:
            loss: 损失张量

        Returns:
            torch.Tensor: 缩放后的损失张量
        """
        if not self.enabled:
            return loss

        # 注意：在PyTorch的混合精度训练中，我们不应该直接调用scaler.scale
        # 而是直接返回原始损失，然后在backward之后使用step_optimizer方法
        return loss

    def unscale_optimizer(self, optimizer: torch.optim.Optimizer) -> None:
        """反缩放优化器梯度

        Args:
            optimizer: 优化器实例
        """
        if self.enabled:
            self.scaler.unscale_(optimizer)

    def step_optimizer(self, optimizer: torch.optim.Optimizer, stream: Any = None) -> None:
        """执行优化器步骤，必须指定CUDA流

        Args:
            optimizer: 优化器实例
            stream: CUDA流，如果为None则使用默认流
        """
        # 如果CUDA不可用，直接执行内部方法
        if not cuda_manager.is_cuda_available:
            self._step_optimizer_internal(optimizer)
            return

        # 如果没有指定流，使用默认流
        if stream is None:
            stream = cuda_manager.get_current_stream()
            if stream is None:
                stream = cuda_manager.create_stream("default_optimizer_stream")

        # 在指定流上下文中执行
        with torch.cuda.stream(stream):
            self._step_optimizer_internal(optimizer)

    def _step_optimizer_internal(self, optimizer: torch.optim.Optimizer) -> None:
        """内部优化器步骤实现

        Args:
            optimizer: 优化器实例
        """
        if not self.enabled:
            optimizer.step()
            return

        try:
            # 尝试直接执行步骤
            self.scaler.step(optimizer)
        except AssertionError as e:
            if "No inf checks were recorded for this optimizer" in str(e):
                # 如果错误是因为没有记录inf检查，先unscale再尝试
                try:
                    self.scaler.unscale_(optimizer)
                    optimizer.step()
                except Exception as inner_e:
                    error_msg = f"优化器步骤失败: {inner_e!s}"
                    self.logger.error(error_msg)
                    raise RuntimeError(error_msg) from inner_e
            else:
                # 其他断言错误，直接抛出
                raise

    def update_scaler(self, found_inf: bool = False, stream: Any = None) -> None:
        """更新缩放器，必须指定CUDA流

        Args:
            found_inf: 是否发现无穷大值
            stream: CUDA流，如果为None则使用默认流
        """
        # 如果CUDA不可用，直接执行内部方法
        if not cuda_manager.is_cuda_available:
            self._update_scaler_internal(found_inf)
            return

        # 如果没有指定流，使用默认流
        if stream is None:
            stream = cuda_manager.get_current_stream()
            if stream is None:
                stream = cuda_manager.create_stream("default_scaler_stream")

        # 在指定流上下文中执行
        with torch.cuda.stream(stream):
            self._update_scaler_internal(found_inf)

    def _update_scaler_internal(self, found_inf: bool = False) -> None:
        """内部缩放器更新实现

        Args:
            found_inf: 是否发现无穷大值（保留参数以保持接口兼容性）
        """
        # 忽略 found_inf 参数，因为 PyTorch 的 scaler.update() 会自动检测
        if not self.enabled:
            return

        # 移除兼容性处理，直接调用标准API
        try:
            self.scaler.update()
        except Exception as e:
            # 如果失败，这是一个严重错误，应该抛出异常
            error_msg = f"更新缩放器失败: {e!s}"
            self.logger.error(error_msg)
            raise RuntimeError(error_msg) from e

    def check_gradients(self, parameters) -> bool:
        """检查梯度是否有效

        Args:
            parameters: 模型参数

        Returns:
            bool: 梯度是否有效
        """
        if not self.enabled:
            return True

        for param in parameters:
            if param.grad is not None and (
                torch.isnan(param.grad).any() or
                torch.isinf(param.grad).any()
            ):
                return False
        return True

    def get_state_dict(self) -> dict[str, Any]:
        """获取状态字典，用于保存检查点

        Returns:
            Dict[str, Any]: 状态字典
        """
        if not self.enabled:
            return {}

        return {
            'scaler': self.scaler.state_dict(),
            'enabled': self.enabled,
            'dtype': str(self.dtype),
            'init_scale': self.init_scale,
            'growth_factor': self.growth_factor,
            'backoff_factor': self.backoff_factor,
            'growth_interval': self.growth_interval
        }

    def load_state_dict(self, state_dict: dict[str, Any]) -> None:
        """从状态字典加载，用于恢复检查点

        Args:
            state_dict: 状态字典
        """
        if not self.enabled or not state_dict:
            return

        try:
            if 'scaler' in state_dict:
                self.scaler.load_state_dict(state_dict['scaler'])
                self.logger.info(f"已加载梯度缩放器状态 (scale={self.scaler.get_scale()})")
        except Exception as e:
            self.logger.error(f"加载梯度缩放器状态失败: {e!s}")


# 创建全局实例
_amp_manager_instances: dict[str, AMPManager] = {}

def get_amp_manager(name: str = "default", config: Any = None) -> AMPManager:
    """获取混合精度训练管理器实例

    Args:
        name: 管理器名称
        config: 混合精度配置对象

    Returns:
        AMPManager: 混合精度训练管理器实例
    """
    global _amp_manager_instances

    if name not in _amp_manager_instances:
        _amp_manager_instances[name] = AMPManager(config, name)

    return _amp_manager_instances[name]
