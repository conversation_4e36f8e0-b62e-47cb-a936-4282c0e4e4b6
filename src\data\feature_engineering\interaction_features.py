"""特征交互生成器 - 计算特征交互项

此模块负责计算特征之间的交互项，通过特征乘积捕捉特征间的非线性关系。
它现在包含更高级的候选特征选择机制。
"""

from typing import Any

import numpy as np
import pandas as pd
import torch

from src.data.feature_engineering.base import BaseFeatureGenerator
from src.utils.config.manager import ConfigManager  # 导入真实的类
from src.utils.logger import get_logger


class InteractionFeatureGenerator(BaseFeatureGenerator):
    """特征交互生成器

    计算特征之间的交互项，具有可选的高级候选特征选择功能。
    """

    def __init__(self, config: ConfigManager):
        """初始化特征交互生成器

        Args:
            config: 配置管理器实例

        Raises:
            TypeError: 如果 config 不是 ConfigManager 实例
            ValueError: 如果配置无效
            AttributeError: 如果配置结构不完整
        """
        # 检查配置对象类型
        if not isinstance(config, ConfigManager):
            error_msg = "config参数必须为ConfigManager实例"
            self.logger.error(error_msg)
            raise TypeError(error_msg)

        self.config = config
        self.logger = get_logger(__name__)
        self.feature_names: list[str] = []
        self._feature_count = 0
        self._enabled: bool = False
        self.candidate_selection_config: dict[str, Any] | None = None

        try:
            if not hasattr(self.config, 'feature_engineering'):
                self.logger.warning("配置中缺少 feature_engineering 配置项，特征交互生成器将禁用。")
                self._enabled = False # Ensure disabled
                return

            interaction_cfg_obj = getattr(self.config.feature_engineering, 'interaction_features', None)
            if interaction_cfg_obj is None:
                self.logger.warning("配置中缺少 'feature_engineering.interaction_features' 部分，特征交互生成器将禁用。")
                self._enabled = False # Ensure disabled
                return

            self._enabled = getattr(interaction_cfg_obj, 'enable', False)

            # 获取候选特征选择配置
            cs_config_obj = getattr(interaction_cfg_obj, 'candidate_selection', None)

            # 检查配置是否存在并启用
            if cs_config_obj is not None and getattr(cs_config_obj, 'enable', False):
                    # 获取子配置
                    lag_corr_conf_obj = getattr(cs_config_obj, 'lag_corr', None)
                    mutual_info_conf_obj = getattr(cs_config_obj, 'mutual_info', None)

                    # 获取方法列表
                    methods_attr = getattr(cs_config_obj, 'methods', [])
                    methods_list = list(methods_attr) if methods_attr else []

                    # 创建配置字典
                    self.candidate_selection_config = {
                        'enable': True,
                        'methods': methods_list,
                        'combination_logic': getattr(cs_config_obj, 'combination_logic', 'union'),
                        'top_n_final_candidates': getattr(cs_config_obj, 'top_n_final_candidates', None)
                    }

                    # 添加滞后相关性配置
                    if lag_corr_conf_obj is not None:
                        # 如果lag_corr_conf_obj是字典，直接使用
                        if isinstance(lag_corr_conf_obj, dict):
                            self.candidate_selection_config['lag_corr'] = lag_corr_conf_obj
                        else:
                            # 否则，从对象中获取属性
                            self.candidate_selection_config['lag_corr'] = {
                                'enable': getattr(lag_corr_conf_obj, 'enable', False),
                                'max_lag': getattr(lag_corr_conf_obj, 'max_lag', 1),
                                'abs_corr_threshold': getattr(lag_corr_conf_obj, 'abs_corr_threshold', 0.0)
                            }
                    else:
                        # 从配置文件中获取
                        self.candidate_selection_config['lag_corr'] = cs_config_obj.lag_corr if hasattr(cs_config_obj, 'lag_corr') else {'enable': False}

                    # 添加互信息配置
                    if mutual_info_conf_obj is not None:
                        # 如果mutual_info_conf_obj是字典，直接使用
                        if isinstance(mutual_info_conf_obj, dict):
                            self.candidate_selection_config['mutual_info'] = mutual_info_conf_obj
                        else:
                            # 否则，从对象中获取属性
                            self.candidate_selection_config['mutual_info'] = {
                                'enable': getattr(mutual_info_conf_obj, 'enable', False),
                                'mi_threshold': getattr(mutual_info_conf_obj, 'mi_threshold', 0.0)
                            }
                    else:
                        # 从配置文件中获取
                        self.candidate_selection_config['mutual_info'] = cs_config_obj.mutual_info if hasattr(cs_config_obj, 'mutual_info') else {'enable': False}

                    # 检查方法列表
                    if not self.candidate_selection_config['methods']:
                        self.logger.warning("候选特征选择已启用但未指定方法。将无法生成特征。")

                    # 确保至少一种方法被启用
                    lag_corr_enabled = self.candidate_selection_config.get('lag_corr', {}).get('enable', False)
                    mutual_info_enabled = self.candidate_selection_config.get('mutual_info', {}).get('enable', False)

                    if not (lag_corr_enabled or mutual_info_enabled):
                        self.logger.warning("候选特征选择已启用但所有方法都被禁用。将无法生成特征。")
            else:
                self.logger.info("高级候选特征选择未启用或未配置。")
                self.candidate_selection_config = None

        except AttributeError as e:
            self.logger.error(f"读取特征交互配置时缺少必要的属性: {e}。生成器将禁用。")
            self._enabled = False
            return
        except Exception as e_init:
            self.logger.error(f"初始化 InteractionFeatureGenerator 失败: {e_init}", exc_info=True)
            self._enabled = False
            return


        self.logger.info(f"特征交互已{'启用' if self._enabled else '禁用'}")
        if self._enabled and self.candidate_selection_config and self.candidate_selection_config.get('enable'):
            self.logger.info(f"高级候选特征选择已启用。配置: {self.candidate_selection_config}")
        elif self._enabled:
            self.logger.warning("高级候选特征选择未启用，且不使用回退策略，特征交互将不会生成任何特征。")


    def generate(self, data: torch.Tensor, **kwargs) -> torch.Tensor:
        """生成特征交互项

        Args:
            data: 输入数据张量 [n_samples, n_features]
            **kwargs: 其他参数，可能包含 'target_series' (pd.Series) 用于高级候选特征选择

        Returns:
            torch.Tensor: 生成的特征交互项张量 [n_samples, n_interactions]

        Raises:
            ValueError: 如果输入特征不足或无法生成交互项，或配置无效
        """
        if not self.is_enabled:
            self.logger.info("特征交互生成器已禁用，跳过。")
            return torch.empty((data.shape[0], 0), dtype=data.dtype, device=data.device)

        self.logger.debug(
            f"InteractionFeatureGenerator - 输入数据统计:\n"
            f"- 形状: {data.shape}\n"
            f"- 数据类型: {data.dtype}\n"
            f"- 设备: {data.device}\n"
            f"- 均值(每列): {data.mean(dim=0).tolist() if data.numel() > 0 else 'N/A'}\n"
            f"- 标准差(每列): {data.std(dim=0).tolist() if data.numel() > 0 else 'N/A'}\n"
            f"- 最小值(每列): {data.min(dim=0).values.tolist() if data.numel() > 0 else 'N/A'}\n"
            f"- 最大值(每列): {data.max(dim=0).values.tolist() if data.numel() > 0 else 'N/A'}\n"
            f"- 是否包含NaN: {torch.isnan(data).any().item()}\n"
            f"- 是否包含Inf: {torch.isinf(data).any().item()}"
        )
        if torch.isinf(data).any():
            error_msg = "输入数据包含无穷大值，无法进行交互特征计算"
            self.logger.error(error_msg)
            raise ValueError(error_msg)


        n_samples, n_features = data.shape
        if n_features < 2:
            error_msg = f"输入特征数量 ({n_features}) 少于2，无法生成交互项"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # --- 候选特征选择 ---
        selected_indices: list[int] = [] # 初始化为空列表
        all_method_selected_indices: dict[str, list[int]] = {} # 存储每种方法的结果

        # Extract target_series from kwargs
        target_series_input: pd.Series | None = kwargs.get('target_series')
        target_series_processed: pd.Series | None = None # Will hold the potentially aligned series

        # 检查高级候选特征选择是否配置和启用
        if not (self.candidate_selection_config and self.candidate_selection_config.get('enable', False)):
            error_msg = "特征交互生成器需要启用高级候选特征选择"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        # 检查是否提供了目标序列
        if target_series_input is None:
            error_msg = "特征交互生成器必须提供目标序列(target_series)，但未在kwargs中找到"
            self.logger.error(error_msg)
            raise ValueError(error_msg)
        else:
                # Prepare DataFrame for calculations
                temp_feature_names = [f"temp_feat_{i}" for i in range(n_features)]
                input_features_df = pd.DataFrame(data.cpu().numpy(), columns=pd.Index(temp_feature_names))

                # --- 检查索引对齐 ---
                if input_features_df.index.equals(target_series_input.index):
                    target_aligned = True
                    target_series_processed = target_series_input # 直接使用
                elif len(input_features_df) == len(target_series_input):
                    try:
                        target_series_copy = target_series_input.copy()
                        target_series_copy.index = input_features_df.index
                        target_series_processed = target_series_copy # 使用对齐后的副本
                        target_aligned = True
                        self.logger.warning("目标序列和特征数据索引不一致，已尝试基于长度对齐。")
                    except Exception as e_align:
                        error_msg = f"尝试对齐目标序列索引时出错: {e_align}"
                        self.logger.error(error_msg)
                        raise ValueError(error_msg) from e_align
                else:
                    error_msg = "目标序列和特征数据的索引不一致且长度不同，无法进行可靠选择"
                    self.logger.error(error_msg)
                    raise ValueError(error_msg)

                # --- Proceed with selection only if target is valid and aligned ---
                if target_series_processed is not None and target_aligned:
                    # 目标序列有效且已对齐，可以进行高级选择
                    self.logger.info("执行高级候选特征选择...")

                    # --- 1. Lag Correlation ---
                    # Check config exists and is enabled before accessing nested keys
                    lc_conf = self.candidate_selection_config.get('lag_corr', {})
                    if 'lag_corr' in self.candidate_selection_config.get('methods', []) and lc_conf.get('enable', False):
                        max_l = lc_conf.get('max_lag', 1)
                        abs_corr_thresh = lc_conf.get('abs_corr_threshold', 0.0)
                        self.logger.info(f"执行滞后相关性筛选... 参数: max_lag={max_l}, abs_corr_threshold={abs_corr_thresh}")
                        lag_selected_indices = []
                        for i in range(n_features):
                            feat_series = input_features_df.iloc[:, i]
                            max_abs_corr_for_feat = 0.0
                            for lag in range(1, max_l + 1):
                                if len(feat_series) <= lag:
                                    continue
                                try:
                                    shifted_feat = feat_series.shift(lag)
                                    # target_series_processed is guaranteed not None here

                                    corr_val = 0.0 # 默认相关性为0
                                    std_threshold = 1e-9

                                    # 1. 创建包含两个序列的临时DataFrame并移除NaN行以进行对齐
                                    # 确保target_series_processed的索引与shifted_feat对齐（如果长度相同）
                                    # Pandas的Series.corr会自动处理对齐，但我们在此预先检查以避免警告

                                    # 检查原始序列长度是否足够
                                    if len(shifted_feat.dropna()) < 2 or len(target_series_processed.dropna()) < 2:
                                        self.logger.debug(
                                            f"  Feature {i}, Lag {lag}: 跳过相关性计算，单个序列的有效数据点不足。"
                                        )
                                    else:
                                        # 创建对齐的DataFrame
                                        aligned_df = pd.DataFrame({
                                            'shifted': shifted_feat,
                                            'target': target_series_processed
                                        }).dropna() # dropna会处理对齐问题

                                        # 2. 检查对齐后是否有足够的共同有效数据点
                                        if len(aligned_df) < 2:
                                            self.logger.debug(
                                                f"  Feature {i}, Lag {lag}: 跳过相关性计算，对齐后共同有效数据点不足 ({len(aligned_df)})。"
                                            )
                                        else:
                                            s1_clean = aligned_df['shifted']
                                            s2_clean = aligned_df['target']

                                            # 3. 计算清理后子序列的标准差 (ddof=1)
                                            s1_std = s1_clean.std(ddof=1)
                                            s2_std = s2_clean.std(ddof=1)

                                            # 4. 检查标准差是否过小或为NaN
                                            if pd.isna(s1_std) or s1_std < std_threshold or \
                                               pd.isna(s2_std) or s2_std < std_threshold:
                                                self.logger.debug(
                                                    f"  Feature {i}, Lag {lag}: 跳过相关性计算，清理后标准差过低。"
                                                    f" s1_std: {s1_std:.2e}, s2_std: {s2_std:.2e}"
                                                )
                                            else:
                                                # 5. 如果所有检查通过，则使用清理和对齐后的序列计算相关性
                                                # 计算相关性
                                                corr_val = s1_clean.corr(s2_clean)
                                                if pd.isna(corr_val): # 如果corr仍然是NaN，这是数据质量问题
                                                    error_msg = f"Feature {i}, Lag {lag}: 相关性计算结果为NaN，数据质量问题"
                                                    self.logger.error(error_msg)
                                                    raise ValueError(error_msg)

                                    # Add detailed logging for correlation value
                                    self.logger.debug(f"  Feature {i}, Lag {lag}: Corr = {corr_val:.4f}")
                                    if pd.notna(corr_val) and abs(corr_val) > max_abs_corr_for_feat:
                                        max_abs_corr_for_feat = abs(corr_val) # Update max found so far
                                except Exception as e_corr:
                                    error_msg = f"计算特征 {i} lag {lag} 相关性时出错: {e_corr}"
                                    self.logger.error(error_msg)
                                    raise RuntimeError(error_msg) from e_corr
                            # Check the condition *after* iterating through all lags for feature i
                            self.logger.debug(f"Feature {i}: Max abs lag corr = {max_abs_corr_for_feat:.4f}, Threshold = {abs_corr_thresh}")
                            if max_abs_corr_for_feat >= abs_corr_thresh:
                                lag_selected_indices.append(i) # Add index if max corr meets threshold
                                self.logger.debug(f"Feature {i} selected by lag correlation.")
                            else:
                                 self.logger.debug(f"Feature {i} NOT selected by lag correlation.")

                        # Assign the result after the loop finishes
                        all_method_selected_indices['lag_corr'] = lag_selected_indices
                        self.logger.info(f"滞后相关性筛选后选中: {len(lag_selected_indices)} 特征。指数: {lag_selected_indices}")

                    # --- 2. Mutual Information ---
                    # Check config exists and is enabled before accessing nested keys
                    mi_conf = self.candidate_selection_config.get('mutual_info', {})
                    if 'mutual_info' in self.candidate_selection_config.get('methods', []) and mi_conf.get('enable', False):
                        mi_thresh_param = mi_conf.get('mi_threshold', 0.0)
                        self.logger.info(f"执行互信息筛选... 参数: mi_threshold={mi_thresh_param}")
                        try:
                            from sklearn.feature_selection import mutual_info_regression
                            # Use the already aligned and potentially NaN-handled data
                            mi_input_df = input_features_df.copy()
                            mi_target_series_clean = target_series_processed.copy() # Already aligned

                            # Drop rows with NaN in target first
                            initial_rows_target = len(mi_target_series_clean)
                            valid_target_idx = mi_target_series_clean.dropna().index
                            rows_after_target_dropna = len(valid_target_idx)
                            self.logger.debug(f"互信息 - 目标序列 dropna: {initial_rows_target} -> {rows_after_target_dropna} 行")
                            mi_input_df = mi_input_df.loc[valid_target_idx]
                            mi_target_series_clean = mi_target_series_clean.loc[valid_target_idx]

                            # Drop rows with NaN in features
                            initial_rows_features = len(mi_input_df)
                            valid_feature_idx = mi_input_df.dropna().index
                            rows_after_features_dropna = len(valid_feature_idx)
                            self.logger.debug(f"互信息 - 特征数据 dropna: {initial_rows_features} -> {rows_after_features_dropna} 行")
                            mi_input_df = mi_input_df.loc[valid_feature_idx]
                            mi_target_series_clean = mi_target_series_clean.loc[valid_feature_idx]

                            self.logger.debug(f"MI calculation using {len(mi_input_df)} non-NaN rows.")

                            if mi_input_df.empty or mi_target_series_clean.empty:
                                error_msg = "处理NaN后，没有足够的数据进行互信息计算"
                                self.logger.error(error_msg)
                                raise ValueError(error_msg)
                            else:
                                mi_scores = mutual_info_regression(
                                    mi_input_df, mi_target_series_clean,
                                    random_state=getattr(self.config.system, 'seed', None)
                                )
                                mi_thresh = mi_conf.get('mi_threshold', 0.0)
                                original_indices = np.arange(n_features)
                                mi_selected_indices = original_indices[mi_scores >= mi_thresh].tolist()
                                all_method_selected_indices['mutual_info'] = mi_selected_indices
                                self.logger.info(f"互信息筛选后选中: {len(mi_selected_indices)} 特征。指数: {mi_selected_indices}")
                        except ImportError:
                            error_msg = "scikit-learn 未安装，无法执行互信息筛选"
                            self.logger.error(error_msg)
                            raise ImportError(error_msg) from None
                        except Exception as e_mi:
                            error_msg = f"互信息筛选失败: {e_mi}"
                            self.logger.error(error_msg, exc_info=True)
                            raise RuntimeError(error_msg) from e_mi

                    # --- 3. Combine Results ---
                    if not all_method_selected_indices:
                        error_msg = "没有候选选择方法成功执行或产生结果"
                        self.logger.error(error_msg)
                        raise ValueError(error_msg)
                    # 确保candidate_selection_config不为None
                    elif self.candidate_selection_config is None:
                        error_msg = "内部错误: candidate_selection_config为None"
                        self.logger.error(error_msg)
                        raise ValueError(error_msg)
                    else:
                        final_candidates_set = set()
                        logic = self.candidate_selection_config.get('combination_logic', 'union')

                        if logic == 'union':
                            for method_indices in all_method_selected_indices.values():
                                final_candidates_set.update(method_indices)
                        elif logic == 'intersection':
                            first_key = next((k for k in ['lag_corr', 'mutual_info'] if k in all_method_selected_indices), None)
                            if first_key:
                                final_candidates_set = set(all_method_selected_indices[first_key])
                                for method_key, method_indices in all_method_selected_indices.items():
                                    if method_key != first_key:
                                        final_candidates_set.intersection_update(method_indices)
                            else:
                                error_msg = "指定了交集逻辑，但没有方法产生结果"
                                self.logger.error(error_msg)
                                raise ValueError(error_msg)
                        else:
                            error_msg = f"未知的组合逻辑: {logic}，必须为 'union' 或 'intersection'"
                            self.logger.error(error_msg)
                            raise ValueError(error_msg)

                        selected_indices = sorted(final_candidates_set) # 分配组合结果
                        self.logger.info(f"组合方法 ('{logic}') 后选中: {len(selected_indices)} 特征。指数: {selected_indices}")

                        # --- 4. Apply top_n_final_candidates ---
                        if selected_indices and self.candidate_selection_config: # 检查两者
                            top_n_final = self.candidate_selection_config.get('top_n_final_candidates')
                            if top_n_final is not None and isinstance(top_n_final, int) and top_n_final > 0 and len(selected_indices) > top_n_final:
                                    self.logger.info(f"应用 top_n_final_candidates: {top_n_final}，从 {len(selected_indices)} 筛选。")
                                    # TODO: 更智能的 top_n 选择
                                    selected_indices = selected_indices[:top_n_final]
                                    self.logger.info(f"应用 top_n_final_candidates 后选中: {len(selected_indices)} 特征。指数: {selected_indices}")

                    # --- 检查是否有足够的特征进行交互 ---
                    if len(selected_indices) < 2:
                        error_msg = "高级筛选未选中足够（>=2）的有效特征"
                        self.logger.error(error_msg)
                        raise ValueError(error_msg)
                # End of `if target_series_processed is not None and target_aligned:` block
            # End of `if target_series_input is None:` block
        # End of `if self.candidate_selection_config and ...` block

        # 不再需要回退策略，因为我们在前面已经抛出了异常


        # --- Interaction Term Generation ---
        if len(selected_indices) < 2:
            error_msg = f"最终选定的候选特征数量 ({len(selected_indices)}) 少于2，无法生成交互项"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        self.logger.info(f"最终选定 {len(selected_indices)} 个特征进行交互，其在原始输入中的索引为: {selected_indices}")

        selected_data_for_interaction = data[:, selected_indices]
        num_selected = selected_data_for_interaction.shape[1]

        interactions = []
        current_feature_names = []

        for i in range(num_selected):
            for j in range(i + 1, num_selected):
                interaction_term = selected_data_for_interaction[:, i] * selected_data_for_interaction[:, j]
                interactions.append(interaction_term)
                original_idx_i = selected_indices[i]
                original_idx_j = selected_indices[j]
                current_feature_names.append(f"interaction_origIdx{original_idx_i}_x_origIdx{original_idx_j}")

        if not interactions:
            error_msg = "未能生成任何交互项（在组合循环后）"
            self.logger.error(error_msg)
            raise ValueError(error_msg)

        interactions_tensor = torch.stack(interactions, dim=1)
        self._feature_count = interactions_tensor.shape[1]
        self.feature_names = current_feature_names

        self.logger.info(f"成功生成特征交互项 | 维度: {interactions_tensor.shape}")
        return interactions_tensor

    def get_feature_names(self) -> list[str]:
        return self.feature_names

    @property
    def feature_count(self) -> int:
        return self._feature_count

    @property
    def is_enabled(self) -> bool:
        return self._enabled
